import { useCallback, useContext, useEffect, useState } from 'react';
import {
  View,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Keyboard,
  SafeAreaView,
} from 'react-native';
import styles from '../../assets/styles/SignUp';
import {
  signIn,
  getCurrentUser,
} from 'aws-amplify/auth';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { parseAWSAuthError, validateInputs } from '../../utils/utils';
import { Fonts, isSmallDevice, useEmail, useName } from '../../utils';
import { ypdDarkGrey, ypdRed, ypdWhite } from '../../utils/colors';
import CustomButton from '../../components/CustomButton';
import Heading from '../../components/Heading';
import Input from '../../components/Input';
import { setUsername, setUserId, updateProfile } from '../../redux/slices/profileSlice';
import { useDispatch, useSelector } from 'react-redux';
import { Hub } from '@aws-amplify/core';
import Apple from '../../assets/svgs/profileBuilder/Apple';
import Google from '../../assets/svgs/profileBuilder/Google';
import AuthWarning from '../../components/AuthWarnings';
import { getUserProfile } from '../../api/profile';
import { recordEvent } from '../../api/events';
import { ThemeContext } from '../../context/ThemeContext';

const Login = () => {
  const name = useName();
  const { theme } = useContext(ThemeContext);
  const dispatch = useDispatch();
  const navigation = useNavigation();

  const [email, setEmail] = useState(useEmail());
  const [screenTime, setScreenTime] = useState(null);
  const [password, setPassword] = useState('');
  const [focusedInput, setFocusedInput] = useState(null);
  const [warningMessage, setWarningMessage] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoginSubmitting, setIsLoginSubmitting] = useState(false);
  const [isAppleSubmitting, setIsAppleSubmitting] = useState(false);
  const [isGoogleSubmitting, setIsGoogleSubmitting] = useState(false);

  const isAnySubmitting =
    isLoginSubmitting || isGoogleSubmitting || isAppleSubmitting;

  useFocusEffect(
    useCallback(() => {
      setScreenTime(new Date());
    }, []),
  );

  const saveAuthEvent = async (username) => {
    const createdAt = new Date().toISOString();
    const attributes = {
      currentScreen: 'login',
      screenTime: (new Date() - screenTime) / 1000,
    };
    await recordEvent(
      'Auth Event',
      username,
      true,
      '#A6',
      attributes,
      createdAt,
    );
  };

  // OAuth Hub listener disabled due to social login being temporarily unavailable
  // useEffect(() => {
  //   let timeout;

  //   const unsubscribe = Hub.listen('auth', ({ payload }) => {
  //     switch (payload.event) {
  //       case 'signInWithRedirect':
  //         getUser();
  //         break;
  //       case 'signInWithRedirect_failure':
  //         setWarningMessage('Social sign-in failed. Please try again.');
  //         setIsGoogleSubmitting(false);
  //         setIsAppleSubmitting(false);
  //         break;
  //       case 'customOAuthState':
  //         console.log(payload.data);
  //         break;
  //     }
  //   });

  //   return () => {
  //     clearTimeout(timeout);
  //     unsubscribe();
  //   };
  // }, []);

  // Function to determine which step to redirect to based on missing profile data
  const getIncompleteProfileStep = (profileData) => {
    if (!profileData) {
      return 'Step1'; // No profile at all, start from beginning
    }

    // Check each required field in order and return the appropriate step
    if (!profileData.name || profileData.name.trim() === '') {
      return 'Step1'; // Name missing
    }

    if (!profileData.age || profileData.age === '') {
      return 'Step2'; // Age missing
    }

    if (!profileData.gender || profileData.gender === '') {
      return 'Step3'; // Gender missing
    }

    if (!profileData.problems || profileData.problems.length === 0) {
      return 'Step4'; // Health concerns missing
    }

    // If we reach here, basic profile is complete but might be missing other data
    // Default to Step5 for cannabis usage or other advanced settings
    return 'Step5';
  };

  const getUser = async () => {
    try {
      console.log('[Login] Getting current user...');
      const { userId, username } = await getCurrentUser();
      console.log('[Login] Current user:', { userId, username });

      console.log('[Login] Fetching user profile...');
      let profileData = null;
      try {
        profileData = await getUserProfile(userId);
        console.log('[Login] Profile data:', profileData);
      } catch (profileError) {
        console.log('[Login] Profile not found or error fetching profile:', profileError);
        profileData = null;
      }

      // Check if profile is complete
      const isProfileComplete = profileData &&
        profileData.name && profileData.name.trim() !== '' &&
        profileData.age && profileData.age !== '' &&
        profileData.gender && profileData.gender !== '' &&
        profileData.problems && profileData.problems.length > 0;

      console.log('[Login] Profile complete:', isProfileComplete);

      if (isProfileComplete) {
        // Profile is complete, proceed to main app
        console.log('[Login] Proceeding to main app...');
        dispatch(updateProfile(profileData));
        await saveAuthEvent(profileData.username);
        navigation.reset({
          index: 0,
          routes: [
            {
              name: 'Tabs',
              state: {
                routes: [{ name: 'HomeScreen' }],
              },
            },
          ],
        });
      } else {
        // User exists but profile is incomplete or missing
        console.log('[Login] Profile incomplete, determining redirect step...');

        // If we have partial profile data, use it; otherwise create minimal profile
        const profileForStepCheck = profileData || {
          userId: userId,
          username: username,
          name: '',
          age: '',
          gender: '',
          problems: []
        };

        // Update Redux with any existing profile data
        if (profileData) {
          dispatch(updateProfile(profileData));
        } else {
          // Set basic user info for new profiles
          dispatch(setUserId(userId));
          dispatch(setUsername(username));
        }

        // Determine which step to redirect to based on missing data
        const redirectStep = getIncompleteProfileStep(profileForStepCheck);
        console.log('[Login] Redirecting to step:', redirectStep);

        setWarningMessage('Profile incomplete. Redirecting to complete your profile...');
        setTimeout(async () => {
          try {
            setWarningMessage('');
            // Navigate to the appropriate step based on missing data
            console.log('[Login] Navigating to:', redirectStep);
            navigation.navigate(redirectStep);
          } catch (navError) {
            console.log('[Login] Navigation error:', navError);
            // Fallback to Step1 if navigation fails
            navigation.navigate('Step1');
          }
        }, 3000);
      }
    } catch (error) {
      console.log('[Login] Error in getUser:', error);
      await handleLoginError(error);
    } finally {
      setIsGoogleSubmitting(false);
      setIsAppleSubmitting(false);
    }
  };

  const handleLogin = async () => {
    if (isAnySubmitting) return;
    const error = validateInputs(email, true, password, true);
    if (error) {
      setWarningMessage(error);
      return;
    }
    setIsLoginSubmitting(true);
    try {
      const response = await signIn({ username: email, password });

      if (response['nextStep']['signInStep'] === 'CONFIRM_SIGN_UP') {
        dispatch(setUsername(email));
        await saveAuthEvent(email);
        navigation.navigate('ConfirmSignUp', { password });
      } else {
        await getUser();
      }
    } catch (error) {
      await handleLoginError(error);
    }
  };

  const handleProvidersSignup = async (provider) => {
    if (isAnySubmitting) return;

    // Temporary workaround for AWS Amplify OAuth issue in React Native
    // The "Cannot assign to property 'search'" error is a known issue with signInWithRedirect
    setWarningMessage(`${provider} sign-in is temporarily unavailable. Please use email/password login.`);
    return;

    /*
    // Original code - disabled due to OAuth redirect issue
    if (provider === 'Apple') setIsAppleSubmitting(true);
    if (provider === 'Google') setIsGoogleSubmitting(true);

    const timeout = setTimeout(() => {
      if (provider === 'Apple') setIsAppleSubmitting(false);
      if (provider === 'Google') setIsGoogleSubmitting(false);
    }, 20000);

    try {
      await signInWithRedirect({ provider });
    } catch (error) {
      clearTimeout(timeout);
      await handleLoginError(error);
    }
    */
  };

  const handleLoginError = async (error) => {
    console.log('[Login] Error during login:', error);
    console.log('[Login] Error name:', error?.name);
    console.log('[Login] Error message:', error?.message);
    console.log('[Login] Error stack:', error?.stack);

    try {
      let errorMessage = parseAWSAuthError(error);

      if (errorMessage === 'There is already a signed in user.') {
        console.log('[Login] User already signed in, calling getUser...');
        await getUser();
        return;
      }

      if (errorMessage === 'PreSignUp failed with error Email already in use.') {
        errorMessage = 'Email already in use.';
      }
      setWarningMessage(errorMessage);
    } catch (parseError) {
      console.log('[Login] Error parsing AWS error:', parseError);
      setWarningMessage('An unexpected error occurred. Please try again.');
    }

    setIsLoginSubmitting(false);
    setIsGoogleSubmitting(false);
    setIsAppleSubmitting(false);
  };

  return (
    <>
      {warningMessage && (
        <AuthWarning
          text={warningMessage}
          icon="alert-outline"
          size={30}
          iconColor={ypdRed}
          showCloseButton={true}
          onClose={() => setWarningMessage('')}
        />
      )}
      <SafeAreaView
        style={{ flex: 1, backgroundColor: theme.colors.background }}
      >
        <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding">
          <TouchableWithoutFeedback
            onPress={Keyboard.dismiss}
            accessible={false}
          >
            <View style={styles.container}>
              <Heading
                text={name ? `${name}, welcome back!` : 'Welcome back!'}
                size="lg"
                fontFamily={Fonts.MEDIUM}
                color={
                  focusedInput === 'email'
                    ? theme.colors.text
                    : theme.colors.textSecondary
                }
                style={isSmallDevice ? styles.headingSmall : styles.heading}
              />
              <View style={styles.wrapper}>
                <View style={styles.inputContainer}>
                  <Heading
                    text="Email"
                    fontSize={12}
                    style={styles.inputHeading}
                    color={theme.colors.text}
                  />
                  <Input
                    placeholder="Enter your email"
                    value={email}
                    onChangeText={setEmail}
                    keyboardType="email-address"
                    onFocus={() => setFocusedInput('email')}
                    onBlur={() => setFocusedInput(null)}
                    autoCapitalize="none"
                    autoCorrect={false}
                    accessibilityLabel="Email input"
                    disabled={isAnySubmitting}
                  />
                </View>

                <View style={styles.inputContainer}>
                  <Heading
                    text="Password"
                    fontSize={12}
                    style={styles.inputHeading}
                    color={theme.colors.text}
                  />
                  <Input
                    placeholder="Enter your password"
                    value={password}
                    onChangeText={setPassword}
                    secureTextEntry={!showPassword}
                    onFocus={() => setFocusedInput('password')}
                    onBlur={() => setFocusedInput(null)}
                    accessibilityLabel="Password input"
                    showIcon={true}
                    iconName={showPassword ? 'eye' : 'eye-off'}
                    onIconPress={() => setShowPassword(!showPassword)}
                    disabled={isAnySubmitting}
                    style={[{ color: theme.colors.text }]}
                  />
                  <Heading
                    text="I forgot my password"
                    size="link"
                    style={[
                      styles.forgotPassword,
                      { color: theme.colors.linkText },
                    ]}
                    onPress={() => navigation.navigate('ResetPassword')}
                  />
                </View>
                <View style={styles.buttonContainer}>
                  <CustomButton
                    title="Log in"
                    onPress={handleLogin}
                    variant="green"
                    width="100%"
                    disabled={isAnySubmitting}
                    activityIndicator={isLoginSubmitting}
                  />
                </View>
                <View style={styles.dividerContainer}>
                  <View style={styles.divider} />
                  <Heading
                    text="or"
                    size="xsSmall"
                    style={styles.dividerText}
                    color={theme.colors.text}
                  />
                  <View style={styles.divider} />
                </View>
                <CustomButton
                  title="Continue with Google"
                  width="100%"
                  variant="lightoutline"
                  onPress={() => handleProvidersSignup('Google')}
                  fontSize={14}
                  size={20}
                  textColor={theme.colors.text}
                  svgIcon={<Google />}
                  style={[styles.googleButton, { color: theme.colors.text }]}
                  disabled={isAnySubmitting}
                  activityIndicator={isGoogleSubmitting}
                />
                <CustomButton
                  title="Continue with Apple"
                  width="100%"
                  variant="lightoutline"
                  onPress={() => handleProvidersSignup('Apple')}
                  fontSize={14}
                  size={20}
                  textColor={theme.colors.text}
                  svgIcon={<Apple />}
                  disabled={isAnySubmitting}
                  activityIndicator={isAppleSubmitting}
                />
              </View>
            </View>
          </TouchableWithoutFeedback>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </>
  );
};

export default Login;
