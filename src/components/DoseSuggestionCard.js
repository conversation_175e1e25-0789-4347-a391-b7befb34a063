import { View } from 'react-native';
import Heading from './Heading';
import { Fonts } from '../utils';
import CustomButton from './CustomButton';
import styles from '../assets/styles/DoseSuggestionCard.scss';
import {
  ypdBlue,
  ypdGreen,
  ypdLightGrey,
  ypdLink,
  ypdOldGreen,
  ypdShadow,
  ypdTeal,
  ypdWhite,
} from '../utils/colors';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import { useContext, useEffect, useState } from 'react';
import { Shadow } from 'react-native-shadow-2';
import { ThemeContext } from '../context/ThemeContext';

const DoseSuggestionCard = ({ lastCheckinMap, starFor = [] }) => {
  const { theme } = useContext(ThemeContext);
  const navigation = useNavigation();

  const dosage = useSelector((state) => state.profile.initialDose);
  const dosageType = useSelector((state) => state.profile.dosageType);
  const cannabisType = useSelector((state) => state.profile.cannabisType);

  const [thciEnabled, setThciEnabled] = useState(true);
  const [thcoEnabled, setThcoEnabled] = useState(true);
  const [cbdiEnabled, setCbdiEnabled] = useState(true);
  const [cbdoEnabled, setCbdoEnabled] = useState(true);

  const [thciRemaining, setThciRemaining] = useState(0);
  const [thcoRemaining, setThcoRemaining] = useState(0);
  const [cbdiRemaining, setCbdiRemaining] = useState(0);
  const [cbdoRemaining, setCbdoRemaining] = useState(0);

  useEffect(() => {
    setThciEnabled(
      ['both', 'thc'].includes(cannabisType) &&
        ['both', 'inhaled'].includes(dosageType),
    );
    setThcoEnabled(
      ['both', 'thc'].includes(cannabisType) &&
        ['both', 'oral'].includes(dosageType),
    );
    setCbdiEnabled(
      ['both', 'cbd'].includes(cannabisType) &&
        ['both', 'inhaled'].includes(dosageType),
    );
    setCbdoEnabled(
      ['both', 'cbd'].includes(cannabisType) &&
        ['both', 'oral'].includes(dosageType),
    );
  }, [cannabisType, dosageType]);

  const thciLastSuggestion = useSelector(
    (state) => state.suggestions.thciLastSuggestion,
  );
  const thcoLastSuggestion = useSelector(
    (state) => state.suggestions.thcoLastSuggestion,
  );
  const cbdiLastSuggestion = useSelector(
    (state) => state.suggestions.cbdiLastSuggestion,
  );
  const cbdoLastSuggestion = useSelector(
    (state) => state.suggestions.cbdoLastSuggestion,
  );

  const thcoValue = `${thcoLastSuggestion?.thco || dosage?.thcOral || 0} mg`;
  const thciValue = `${thciLastSuggestion?.thci || dosage?.thcVape || 0} puffs`;
  const cbdoValue = `${cbdoLastSuggestion?.cbdo || dosage?.cbdOral || 0} mg`;
  const cbdiValue = `${cbdiLastSuggestion?.cbdi || dosage?.cbdVape || 0} puffs`;

  const addOption = () => {
    if (dosageType !== 'both') {
      navigation.navigate('Step7', {
        profileBuilding: false,
        marginTop: 50,
      });
    } else {
      navigation.navigate('Step6', {
        profileBuilding: false,
        marginTop: 50,
      });
    }
  };

  const handleDosePress = (doseType, modality) => {
    navigation.navigate('CalendarModal', {
      modality,
      cannabisType: modality.startsWith('thc') ? 'THC' : 'CBD',
      dosageType: doseType,
      lockoutHours: LOCKOUT_IN_HOURS[modality],
    });
  };

  const LOCKOUT_IN_HOURS = {
    thci: 0.01,
    cbdi: 0.01,
    thco: 0.02,
    cbdo: 0.02,
  };

  const formatTime = (totalSeconds) => {
    const hours = String(Math.floor(totalSeconds / 3600)).padStart(2, '0');
    const minutes = String(Math.floor((totalSeconds % 3600) / 60)).padStart(
      2,
      '0',
    );
    const seconds = String(totalSeconds % 60).padStart(2, '0');
    return `${hours}:${minutes}:${seconds}`;
  };

  useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date();

      const calcRemaining = (lastSuggestion, lockoutHours) => {
        if (!lastSuggestion) {
          return 0;
        }

        const suggestionTime = new Date(lastSuggestion);
        const elapsedMs = now - suggestionTime;
        const lockoutMs = lockoutHours * 60 * 60 * 1000;
        const remainingMs = lockoutMs - elapsedMs;
        return Math.max(0, Math.floor(remainingMs / 1000));
      };

      setThciRemaining(
        thciEnabled
          ? calcRemaining(lastCheckinMap['thci'], LOCKOUT_IN_HOURS.thci)
          : 0,
      );
      setThcoRemaining(
        thcoEnabled
          ? calcRemaining(lastCheckinMap['thco'], LOCKOUT_IN_HOURS.thco)
          : 0,
      );
      setCbdiRemaining(
        cbdiEnabled
          ? calcRemaining(lastCheckinMap['cbdi'], LOCKOUT_IN_HOURS.cbdi)
          : 0,
      );
      setCbdoRemaining(
        cbdoEnabled
          ? calcRemaining(lastCheckinMap['cbdo'], LOCKOUT_IN_HOURS.cbdo)
          : 0,
      );
    }, 1000);

    return () => clearInterval(interval);
  }, [
    thciLastSuggestion,
    thcoLastSuggestion,
    cbdiLastSuggestion,
    cbdoLastSuggestion,
  ]);

  return (
    <View style={styles.container}>
      <Shadow
        offset={[0, 3]}
        distance={1}
        startColor={ypdShadow}
        stretch={true}
      >
        <View
          style={[styles.wrapper, { backgroundColor: theme.colors.background }]}
        >
          <View style={[styles.divider, { backgroundColor: ypdBlue }]} />
          <View style={[styles.buttonRow]}>
            <View>
              <View style={styles.rowContainer}>
                <Heading
                  text="Oral THC"
                  size="md"
                  fontFamily={Fonts.MEDIUM}
                  color={theme.colors.darkTextGray}
                />
                {starFor.includes('thco') && (
                  <Icon name="star" size={24} color={ypdLink} />
                )}
              </View>
              <View style={styles.gap} />
              <CustomButton
                size={22}
                width={145}
                height={55}
                variant="teal"
                alignSelf="center"
                flexDirection="row-reverse"
                iconColor={ypdGreen}
                color={ypdLightGrey}
                iconDividerColor={ypdWhite}
                onPress={() =>
                  thcoEnabled && thcoRemaining === 0
                    ? handleDosePress('oral', 'thco')
                    : addOption()
                }
                disabled={thcoEnabled && thcoRemaining > 0}
                title={
                  thcoEnabled
                    ? thcoRemaining > 0
                      ? formatTime(thcoRemaining)
                      : thcoValue
                    : 'Add this option'
                }
                iconComponent={
                  thcoEnabled
                    ? thcoRemaining > 0
                      ? null
                      : 'plus-circle-outline'
                    : null
                }
                lockText={
                  thcoEnabled && thcoRemaining ? 'Till your next dose' : null
                }
                style={[
                  !thcoEnabled
                    ? styles.customDisabledButton
                    : thcoRemaining > 0
                      ? styles.customTimerButtonUp
                      : theme.dark
                        ? styles.customTealButtonDark
                        : styles.customTealButton,
                ]}
                textStyle={[
                  (!thcoEnabled || thcoRemaining > 0) &&
                    styles.customDisabledText,
                ]}
              />
              <View style={styles.gap} />
              {thcoEnabled && (
                <View style={styles.textContainer}>
                  <Heading
                    text={'By mouth \nEvery 2 hours as needed'}
                    size="xssmall"
                    lineHeight={18}
                    fontFamily={Fonts.MEDIUM}
                    color={theme.colors.text}
                  />
                </View>
              )}
            </View>
            <View>
              <View style={styles.rowContainer}>
                <Heading
                  text="Inhaled THC"
                  size="md"
                  fontFamily={Fonts.MEDIUM}
                  color={theme.colors.darkTextGray}
                />
                {starFor.includes('thci') && (
                  <Icon name="star" size={24} color={ypdLink} />
                )}
              </View>
              <View style={styles.gap} />
              <CustomButton
                size={22}
                width={145}
                height={55}
                variant="teal"
                alignSelf="center"
                flexDirection="row-reverse"
                iconColor={ypdGreen}
                color={ypdLightGrey}
                iconDividerColor={ypdWhite}
                onPress={() =>
                  thciEnabled && thciRemaining === 0
                    ? handleDosePress('inhaled', 'thci')
                    : addOption()
                }
                disabled={thciEnabled && thciRemaining > 0}
                title={
                  thciEnabled
                    ? thciRemaining > 0
                      ? formatTime(thciRemaining)
                      : thciValue
                    : 'Add this option'
                }
                iconComponent={
                  thciEnabled
                    ? thciRemaining > 0
                      ? null
                      : 'plus-circle-outline'
                    : null
                }
                lockText={
                  thciEnabled && thciRemaining ? 'Till your next dose' : null
                }
                style={[
                  !thciEnabled
                    ? styles.customDisabledButton
                    : thciRemaining > 0
                      ? styles.customTimerButtonUp
                      : theme.dark
                        ? styles.customTealButtonDark
                        : styles.customTealButton,
                ]}
                textStyle={[
                  (!thciEnabled || thciRemaining > 0) &&
                    styles.customDisabledText,
                ]}
              />
              <View style={styles.gap} />
              {thciEnabled && (
                <View style={styles.textContainer}>
                  <Heading
                    text={'By vape\nEvery 1 hour as needed'}
                    size="xssmall"
                    lineHeight={18}
                    fontFamily={Fonts.MEDIUM}
                    color={theme.colors.text}
                  />
                </View>
              )}
            </View>
          </View>
        </View>
      </Shadow>
      <Shadow
        offset={[0, 3]}
        distance={1}
        startColor={ypdShadow}
        stretch={true}
      >
        <View
          style={[styles.wrapper, { backgroundColor: theme.colors.background }]}
        >
          <View style={[styles.divider, { backgroundColor: ypdOldGreen }]} />
          <View style={styles.buttonRow}>
            <View>
              <View style={styles.rowContainer}>
                <Heading
                  text="Oral CBD"
                  size="md"
                  fontFamily={Fonts.MEDIUM}
                  color={theme.colors.darkTextGray}
                />
                {starFor.includes('cbdo') && (
                  <Icon name="star" size={24} color={ypdLink} />
                )}
              </View>
              <View style={styles.gap} />
              <CustomButton
                size={22}
                width={145}
                height={55}
                variant="newGreen"
                alignSelf="center"
                flexDirection="row-reverse"
                iconColor={ypdTeal}
                color={ypdTeal}
                iconDividerColor={ypdTeal}
                onPress={() =>
                  cbdoEnabled && cbdoRemaining === 0
                    ? handleDosePress('oral', 'cbdo')
                    : addOption()
                }
                disabled={cbdoEnabled && cbdoRemaining > 0}
                title={
                  cbdoEnabled
                    ? cbdoRemaining > 0
                      ? formatTime(cbdoRemaining)
                      : cbdoValue
                    : 'Add this option'
                }
                iconComponent={
                  cbdoEnabled
                    ? cbdoRemaining > 0
                      ? null
                      : 'plus-circle-outline'
                    : null
                }
                lockText={
                  cbdoEnabled && cbdoRemaining ? 'Till your next dose' : null
                }
                style={[
                  !cbdoEnabled
                    ? styles.customDisabledButton
                    : cbdoRemaining > 0
                      ? styles.customTimerButtonDown
                      : styles.customGreenButton,
                ]}
                textStyle={[
                  (!cbdoEnabled || cbdoRemaining > 0) &&
                    styles.customDisabledText,
                ]}
              />
              <View style={styles.gap} />
              {cbdoEnabled && (
                <View style={styles.textContainer}>
                  <Heading
                    text={'By mouth \nEvery 2 hours as needed'}
                    size="xssmall"
                    lineHeight={18}
                    fontFamily={Fonts.MEDIUM}
                    color={theme.colors.text}
                  />
                </View>
              )}
            </View>
            <View>
              <View style={styles.rowContainer}>
                <Heading
                  text="Inhaled CBD"
                  size="md"
                  fontFamily={Fonts.MEDIUM}
                  color={theme.colors.darkTextGray}
                />
                {starFor.includes('cbdi') && (
                  <Icon name="star" size={24} color={ypdLink} />
                )}
              </View>
              <View style={styles.gap} />
              <CustomButton
                size={22}
                width={145}
                height={55}
                variant="newGreen"
                alignSelf="center"
                flexDirection="row-reverse"
                iconColor={ypdTeal}
                color={ypdTeal}
                iconDividerColor={ypdTeal}
                onPress={() =>
                  cbdiEnabled && cbdiRemaining === 0
                    ? handleDosePress('inhaled', 'cbdi')
                    : addOption()
                }
                disabled={cbdiEnabled && cbdiRemaining > 0}
                title={
                  cbdiEnabled
                    ? cbdiRemaining > 0
                      ? formatTime(cbdiRemaining)
                      : cbdiValue
                    : 'Add this option'
                }
                iconComponent={
                  cbdiEnabled
                    ? cbdiRemaining > 0
                      ? null
                      : 'plus-circle-outline'
                    : null
                }
                lockText={
                  cbdiEnabled && cbdiRemaining ? 'Till your next dose' : null
                }
                style={[
                  !cbdiEnabled
                    ? styles.customDisabledButton
                    : cbdiRemaining > 0
                      ? styles.customTimerButtonDown
                      : styles.customGreenButton,
                ]}
                textStyle={[
                  (!cbdiEnabled || cbdiRemaining > 0) &&
                    styles.customDisabledText,
                ]}
              />
              <View style={styles.gap} />
              {cbdiEnabled && (
                <View style={styles.textContainer}>
                  <Heading
                    text={'By vape\nEvery 1 hour as needed'}
                    size="xssmall"
                    lineHeight={18}
                    fontFamily={Fonts.MEDIUM}
                    color={theme.colors.text}
                  />
                </View>
              )}
            </View>
          </View>
        </View>
      </Shadow>
    </View>
  );
};

export default DoseSuggestionCard;
